# Price Comparison System

A comprehensive price comparison system that analyzes product matches across multiple suppliers and provides a web interface for finding the best deals.

## Overview

This system consists of two main components:

### Part 1: Price Analysis Script
- **File**: `price_comparison_analysis.js`
- **Purpose**: Processes `matches.json` to analyze price relationships between matched products
- **Output**: `price_comparison_results.json` and `price_analysis_summary.json`

### Part 2: Web Application
- **Files**: `price_comparison_app.html`, `price_comparison_app.js`
- **Purpose**: Responsive web interface for searching and comparing products
- **Features**: Filtering by supplier, search functionality, price comparison visualization

## Features

### Price Analysis
- **Data Processing**: Loops through product groups in matches.json
- **Price Extraction**: Identifies all price-related properties from each product
- **Price Categorization**: 
  - "Portion prices": properties containing both "price" and "portion"
  - "Regular prices": properties containing "price" but not "portion"
- **Price Comparison**: Calculates percentage differences using the formula: |price1 - price2| / min(price1, price2) * 100
- **Smart Pricing Rules**: Applies specific calculation rules for different suppliers:
  - **Sysco**: Handles caseprice vs unitprice logic
  - **USFood/Sham**: Filters out products with empty prices
  - **Chef**: Uses unitprice/caseprice structure with portion pricing
  - **Others**: Standard price and portionprice fields

### Classification System
- **Close Matches**: Price difference ≤ 40% (marked with green "Close" badge)
- **Not Close Matches**: Price difference > 40% (marked with red "Not Close" badge)
- **Cheapest Product**: Identified within each group (highlighted with green border and "Cheapest" badge)
- **Weak Match Filtering**: Products with >40% price variance are excluded from cheapest product selection

### Web Interface Features
- **Left Sidebar**:
  - Dropdown filter by supplier/seller
  - Search input for product name or product number
  - Filter by price match status (Close/Not Close)
  - Real-time statistics display
- **Main Content Area**:
  - Responsive product match cards
  - Product details including prices, quantities, unit types
  - Visual indicators for cheapest products and match quality
  - Mobile-friendly design

### Technical Implementation
- **Backend**: Node.js scripts for data processing
- **Frontend**: Pure HTML, CSS, and JavaScript (no frameworks)
- **Data Format**: JSON files for data exchange
- **Responsive Design**: CSS Grid and Flexbox for layout
- **Accessibility**: ARIA labels and keyboard navigation support

## Usage

### Running the Analysis
```bash
node price_comparison_analysis.js
```

This will:
1. Load all seller data files (chef.json, sham.json, sysco.json, etc.)
2. Process matches.json to find product groups
3. Calculate price relationships and variances
4. Generate output files for the web application

### Opening the Web Application
1. Open `price_comparison_app.html` in a web browser
2. The application will automatically load the analysis results
3. Use the sidebar filters to search and filter products
4. Click on product cards to view detailed information

## Output Files

### price_comparison_results.json
- Contains the first 1000 product groups with valid prices
- Structured data for the web application
- Includes product details and price analysis

### price_analysis_summary.json
- High-level statistics and summary information
- Top 100 cheapest products across all groups
- Analysis metadata and timestamps

## Data Structure

### Product Group Structure
```json
{
  "groupId": "group_1",
  "products": [
    {
      "originalProductData": {
        "seller": "chef",
        "productName": "Product Name",
        "productnumber": "12345",
        "packsize": "6/3LB"
      },
      "priceAnalysis": {
        "regularPrice": 100.00,
        "portionPrice": 5.55,
        "unittype": "lb",
        "quantity": 18,
        "isCheapest": true,
        "priceStatus": "close",
        "priceVariancePercentage": 15.5
      }
    }
  ],
  "groupAnalysis": {
    "hasValidPrices": true,
    "cheapestProduct": "chef_12345",
    "maxPriceVariance": 25.0,
    "groupStatus": "close"
  }
}
```

## Supported Suppliers
- **Chef**: Uses unitprice/caseprice structure
- **Sham**: Standard price/portionprice with shamportionprice
- **Sysco**: Caseprice with EA/CS handling
- **USFood**: Standard pricing with packportionprice
- **Greco**: Standard price/portionprice structure
- **Depot**: Pickup/delivery pricing options
- **Perf**: Standard price/portionprice structure

## Price Calculation Rules

1. **Unit Type Conversions**: Automatically converts between oz and lb when needed
2. **Sysco Special Logic**: Handles caseprice ending with EA/CS differently
3. **Empty Price Filtering**: Removes products with no valid pricing data
4. **Variance Capping**: Limits extreme price differences to 1000% to prevent overflow
5. **Cheapest Selection**: Only considers products with ≤40% variance for cheapest designation

## Browser Compatibility
- Modern browsers supporting ES6+ features
- Responsive design works on desktop and mobile devices
- No external dependencies required

## File Dependencies
- `matches.json`: Product matching data
- Seller files: `chef.json`, `sham.json`, `sysco.json`, `usfood.json`, `greco.json`, `depot.json`, `perf.json`
- `price_analysis.js`: Helper functions for price calculations
- `price_comparison_analysis.js`: Main analysis script
- `price_comparison_app.html`: Web application interface
- `price_comparison_app.js`: Frontend JavaScript logic
