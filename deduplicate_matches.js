const fs = require('fs').promises;

// Helper function to manually verify seller count for a group
function verifyGroupSellerCount(group, groupIndex) {
    const sellers = new Set();
    Object.keys(group).forEach(productKey => {
        const seller = productKey.split('_')[0];
        sellers.add(seller);
    });
    console.log(`   🔍 Group ${groupIndex} verification: ${sellers.size} sellers (${Array.from(sellers).join(', ')})`);
    return sellers.size;
}

// Main deduplication function
async function deduplicateMatches() {
    console.log('🔍 Starting matches.json deduplication analysis...\n');

    try {
        // Load the original matches.json file
        console.log('📂 Loading matches.json...');
        const matchesData = await fs.readFile('matches.json', 'utf8');
        const originalMatches = JSON.parse(matchesData);

        console.log(`✅ Loaded ${originalMatches.length} product groups\n`);

        // Skip manual verification for performance

        // Step 1: Analyze product distribution
        console.log('📊 Analyzing product distribution across groups...');
        const productAnalysis = analyzeProductDistribution(originalMatches);

        // Step 2: Implement smart deduplication
        console.log('🧹 Implementing smart deduplication logic...');
        const deduplicationResult = performDeduplication(originalMatches, productAnalysis);

        // Step 3: Generate output files
        console.log('💾 Generating output files...');
        await generateOutputFiles(deduplicationResult);

        // Step 4: Validation and quality assurance
        console.log('✅ Performing validation and quality assurance...');
        const validationResult = validateDeduplication(originalMatches, deduplicationResult);

        // Step 5: Generate summary report
        console.log('📋 Generating summary report...');
        await generateSummaryReport(originalMatches, deduplicationResult, productAnalysis, validationResult);

        console.log('\n🎉 Deduplication process completed successfully!');

    } catch (error) {
        console.error('❌ Error during deduplication process:', error);
        throw error;
    }
}

// Analyze product distribution across all groups
function analyzeProductDistribution(matches) {
    console.log('   📈 Building product inventory...');

    const productInventory = new Map(); // productKey -> { groups: [groupIndex], groupSizes: [size], sellerCounts: [count] }
    const groupSizes = new Map(); // groupIndex -> size
    const groupSellerCounts = new Map(); // groupIndex -> unique seller count

    // First pass: calculate group sizes and seller diversity
    matches.forEach((group, groupIndex) => {
        const groupSize = Object.keys(group).length;
        groupSizes.set(groupIndex, groupSize);

        // Count unique sellers in this group
        const sellers = new Set();
        Object.keys(group).forEach(productKey => {
            const seller = productKey.split('_')[0]; // Extract seller from productKey
            sellers.add(seller);
        });
        groupSellerCounts.set(groupIndex, sellers.size);
    });

    // Second pass: track product appearances
    matches.forEach((group, groupIndex) => {
        const groupSize = groupSizes.get(groupIndex);
        const sellerCount = groupSellerCounts.get(groupIndex);

        Object.keys(group).forEach(productKey => {
            if (!productInventory.has(productKey)) {
                productInventory.set(productKey, {
                    groups: [],
                    groupSizes: [],
                    sellerCounts: [],
                    productName: group[productKey]
                });
            }

            const productInfo = productInventory.get(productKey);
            productInfo.groups.push(groupIndex);
            productInfo.groupSizes.push(groupSize);
            productInfo.sellerCounts.push(sellerCount);
        });
    });

    // Analyze duplication statistics
    let totalProducts = productInventory.size;
    let duplicatedProducts = 0;
    let maxDuplicationCount = 0;
    let totalDuplicationInstances = 0;

    // Calculate seller diversity statistics
    const sellerDiversityStats = {
        minSellers: Math.min(...Array.from(groupSellerCounts.values())),
        maxSellers: Math.max(...Array.from(groupSellerCounts.values())),
        avgSellers: (Array.from(groupSellerCounts.values()).reduce((a, b) => a + b, 0) / groupSellerCounts.size).toFixed(2)
    };

    productInventory.forEach((productInfo, productKey) => {
        if (productInfo.groups.length > 1) {
            duplicatedProducts++;
            maxDuplicationCount = Math.max(maxDuplicationCount, productInfo.groups.length);
            totalDuplicationInstances += productInfo.groups.length - 1; // -1 because we keep one instance
        }
    });

    console.log(`   📊 Analysis Results:`);
    console.log(`      • Total unique products: ${totalProducts.toLocaleString()}`);
    console.log(`      • Products appearing in multiple groups: ${duplicatedProducts.toLocaleString()}`);
    console.log(`      • Maximum appearances of a single product: ${maxDuplicationCount}`);
    console.log(`      • Total duplicate instances to remove: ${totalDuplicationInstances.toLocaleString()}`);
    console.log(`      • Duplication rate: ${((duplicatedProducts / totalProducts) * 100).toFixed(2)}%`);
    console.log(`      • Seller diversity per group: ${sellerDiversityStats.minSellers}-${sellerDiversityStats.maxSellers} (avg: ${sellerDiversityStats.avgSellers})\n`);

    return {
        productInventory,
        groupSizes,
        groupSellerCounts,
        stats: {
            totalProducts,
            duplicatedProducts,
            maxDuplicationCount,
            totalDuplicationInstances,
            sellerDiversity: sellerDiversityStats
        }
    };
}

// Perform the actual deduplication
function performDeduplication(originalMatches, productAnalysis) {
    console.log('   🎯 Determining optimal group placement for each product...');
    console.log('   📊 Using seller diversity as primary priority, group size as secondary...');

    const { productInventory, groupSizes, groupSellerCounts } = productAnalysis;
    const deduplicatedMatches = [];
    const orphanedProducts = [];
    const productPlacements = new Map(); // productKey -> chosenGroupIndex
    const placementReasons = new Map(); // productKey -> reason for placement

    // Step 1: Determine the best group for each product using new prioritization
    productInventory.forEach((productInfo, productKey) => {
        if (productInfo.groups.length === 1) {
            // Product appears in only one group - keep it there
            productPlacements.set(productKey, productInfo.groups[0]);
            placementReasons.set(productKey, 'single_group');
        } else {
            // Product appears in multiple groups - use seller diversity priority
            let bestGroupIndex = productInfo.groups[0];
            let bestSellerCount = productInfo.sellerCounts[0];
            let bestGroupSize = productInfo.groupSizes[0];
            let placementReason = 'seller_diversity';

            // Debug logging for the specific product mentioned
            if (productKey === 'chef_5411046') {
                console.log(`\n🔍 Debug for ${productKey}:`);
                console.log(`   Groups: ${productInfo.groups.join(', ')}`);
                console.log(`   Seller counts: ${productInfo.sellerCounts.join(', ')}`);
                console.log(`   Group sizes: ${productInfo.groupSizes.join(', ')}`);

                // Find the maximum seller count for this product
                const maxSellers = Math.max(...productInfo.sellerCounts);
                console.log(`   🎯 Maximum sellers available: ${maxSellers}`);

                // Show all groups with maximum sellers
                const maxSellerGroups = [];
                for (let j = 0; j < productInfo.groups.length; j++) {
                    if (productInfo.sellerCounts[j] === maxSellers) {
                        maxSellerGroups.push({
                            group: productInfo.groups[j],
                            sellers: productInfo.sellerCounts[j],
                            size: productInfo.groupSizes[j]
                        });
                    }
                }
                console.log(`   📊 Groups with ${maxSellers} sellers:`, maxSellerGroups);
            }

            for (let i = 1; i < productInfo.groups.length; i++) {
                const currentGroupIndex = productInfo.groups[i];
                const currentSellerCount = productInfo.sellerCounts[i];
                const currentGroupSize = productInfo.groupSizes[i];

                // Primary priority: Higher seller count wins
                if (currentSellerCount > bestSellerCount) {
                    bestGroupIndex = currentGroupIndex;
                    bestSellerCount = currentSellerCount;
                    bestGroupSize = currentGroupSize;
                    placementReason = 'seller_diversity';

                    if (productKey === 'chef_5411046') {
                        console.log(`   🎯 New best group ${currentGroupIndex}: ${currentSellerCount} sellers, ${currentGroupSize} products`);
                    }
                }
                // Secondary priority: If seller counts are equal, larger group wins
                else if (currentSellerCount === bestSellerCount && currentGroupSize > bestGroupSize) {
                    bestGroupIndex = currentGroupIndex;
                    bestGroupSize = currentGroupSize;
                    placementReason = 'group_size_tiebreaker';

                    if (productKey === 'chef_5411046') {
                        console.log(`   ⚖️ Tiebreaker group ${currentGroupIndex}: ${currentSellerCount} sellers, ${currentGroupSize} products`);
                    }
                }
            }

            if (productKey === 'chef_5411046') {
                console.log(`   ✅ Final choice: Group ${bestGroupIndex} (${bestSellerCount} sellers, ${bestGroupSize} products)\n`);
            }

            productPlacements.set(productKey, bestGroupIndex);
            placementReasons.set(productKey, placementReason);
        }
    });

    // Step 2: Build deduplicated groups
    console.log('   🔧 Building deduplicated groups...');

    // CORRECT APPROACH: Build groups with only the products assigned to them
    const groupBuilders = new Map(); // chosenGroupIndex -> Set of products assigned to it

    // Collect products for each chosen group
    productPlacements.forEach((chosenGroupIndex, productKey) => {
        if (!groupBuilders.has(chosenGroupIndex)) {
            groupBuilders.set(chosenGroupIndex, new Set());
        }
        groupBuilders.get(chosenGroupIndex).add(productKey);
    });

    // Debug for chef_5411046
    const chef5411046Placement = productPlacements.get('chef_5411046');
    if (chef5411046Placement !== undefined) {
        console.log(`\n🔍 chef_5411046 placement verification:`);
        console.log(`   Chosen group index: ${chef5411046Placement}`);

        const originalGroup = originalMatches[chef5411046Placement];
        const assignedProducts = groupBuilders.get(chef5411046Placement);

        console.log(`   Original group size: ${Object.keys(originalGroup).length}`);
        console.log(`   Products assigned to this group: ${assignedProducts.size}`);
        console.log(`   chef_5411046 in original group: ${!!originalGroup['chef_5411046']}`);
        console.log(`   chef_5411046 assigned to this group: ${assignedProducts.has('chef_5411046')}`);

        // Count sellers in the assigned products
        const sellers = new Set();
        assignedProducts.forEach(productKey => {
            sellers.add(productKey.split('_')[0]);
        });
        console.log(`   Sellers in assigned products: ${sellers.size} (${Array.from(sellers).join(', ')})`);
    }

    // Build the final deduplicated groups
    const sortedGroupIndexes = Array.from(groupBuilders.keys()).sort((a, b) => a - b);

    sortedGroupIndexes.forEach(groupIndex => {
        const originalGroup = originalMatches[groupIndex];
        const assignedProducts = groupBuilders.get(groupIndex);

        if (assignedProducts.size > 0) {
            const deduplicatedGroup = {};

            // Only include products that were assigned to this group
            assignedProducts.forEach(productKey => {
                if (originalGroup[productKey]) {
                    deduplicatedGroup[productKey] = originalGroup[productKey];
                }
            });

            if (Object.keys(deduplicatedGroup).length > 0) {
                deduplicatedMatches.push(deduplicatedGroup);
            }
        }
    });

    // Calculate placement statistics
    const placementStats = {
        sellerDiversity: 0,
        groupSizeTiebreaker: 0,
        singleGroup: 0
    };

    placementReasons.forEach(reason => {
        if (reason === 'seller_diversity') placementStats.sellerDiversity++;
        else if (reason === 'group_size_tiebreaker') placementStats.groupSizeTiebreaker++;
        else if (reason === 'single_group') placementStats.singleGroup++;
    });

    console.log(`   ✅ Deduplication complete:`);
    console.log(`      • Original groups: ${originalMatches.length.toLocaleString()}`);
    console.log(`      • Deduplicated groups: ${deduplicatedMatches.length.toLocaleString()}`);
    console.log(`      • Groups removed (empty after deduplication): ${(originalMatches.length - deduplicatedMatches.length).toLocaleString()}`);
    console.log(`   📊 Placement decisions:`);
    console.log(`      • By seller diversity: ${placementStats.sellerDiversity.toLocaleString()}`);
    console.log(`      • By group size (tiebreaker): ${placementStats.groupSizeTiebreaker.toLocaleString()}`);
    console.log(`      • Single group (no choice): ${placementStats.singleGroup.toLocaleString()}\n`);

    return {
        deduplicatedMatches,
        orphanedProducts,
        productPlacements,
        placementReasons,
        placementStats
    };
}

// Generate output files
async function generateOutputFiles(deduplicationResult) {
    const { deduplicatedMatches, orphanedProducts } = deduplicationResult;

    console.log('   💾 Saving matches-sh.json...');
    await fs.writeFile('matches-sh.json', JSON.stringify(deduplicatedMatches, null, 2));

    console.log('   💾 Saving orphaned-products.json...');
    await fs.writeFile('orphaned-products.json', JSON.stringify(orphanedProducts, null, 2));

    console.log(`   ✅ Output files generated successfully\n`);
}

// Validate the deduplication process
function validateDeduplication(originalMatches, deduplicationResult) {
    console.log('   🔍 Validating deduplication integrity...');

    const { deduplicatedMatches } = deduplicationResult;

    // Count original products
    const originalProducts = new Set();
    originalMatches.forEach(group => {
        Object.keys(group).forEach(productKey => {
            originalProducts.add(productKey);
        });
    });

    // Count deduplicated products
    const deduplicatedProducts = new Set();
    deduplicatedMatches.forEach(group => {
        Object.keys(group).forEach(productKey => {
            deduplicatedProducts.add(productKey);
        });
    });

    // Check for missing products
    const missingProducts = [];
    originalProducts.forEach(productKey => {
        if (!deduplicatedProducts.has(productKey)) {
            missingProducts.push(productKey);
        }
    });

    // Check for duplicate products in deduplicated version
    const productCounts = new Map();
    deduplicatedMatches.forEach(group => {
        Object.keys(group).forEach(productKey => {
            productCounts.set(productKey, (productCounts.get(productKey) || 0) + 1);
        });
    });

    const duplicatesInResult = [];
    productCounts.forEach((count, productKey) => {
        if (count > 1) {
            duplicatesInResult.push({ productKey, count });
        }
    });

    const validationResult = {
        originalProductCount: originalProducts.size,
        deduplicatedProductCount: deduplicatedProducts.size,
        missingProducts,
        duplicatesInResult,
        isValid: missingProducts.length === 0 && duplicatesInResult.length === 0
    };

    if (validationResult.isValid) {
        console.log('   ✅ Validation PASSED: All products preserved, no duplicates remain');
    } else {
        console.log('   ❌ Validation FAILED:');
        if (missingProducts.length > 0) {
            console.log(`      • Missing products: ${missingProducts.length}`);
        }
        if (duplicatesInResult.length > 0) {
            console.log(`      • Duplicate products in result: ${duplicatesInResult.length}`);
        }
    }

    console.log(`   📊 Product count verification:`);
    console.log(`      • Original unique products: ${validationResult.originalProductCount.toLocaleString()}`);
    console.log(`      • Deduplicated unique products: ${validationResult.deduplicatedProductCount.toLocaleString()}`);
    console.log(`      • Products preserved: ${validationResult.originalProductCount === validationResult.deduplicatedProductCount ? '✅ 100%' : '❌ ' + ((validationResult.deduplicatedProductCount / validationResult.originalProductCount) * 100).toFixed(2) + '%'}\n`);

    return validationResult;
}

// Generate comprehensive summary report
async function generateSummaryReport(originalMatches, deduplicationResult, productAnalysis, validationResult) {
    const { deduplicatedMatches, productPlacements, placementStats } = deduplicationResult;
    const { stats, groupSellerCounts } = productAnalysis;

    // Calculate group size distribution
    const originalGroupSizes = originalMatches.map(group => Object.keys(group).length);
    const deduplicatedGroupSizes = deduplicatedMatches.map(group => Object.keys(group).length);

    const originalGroupStats = calculateGroupStats(originalGroupSizes);
    const deduplicatedGroupStats = calculateGroupStats(deduplicatedGroupSizes);

    // Find products that were moved
    const productMovements = [];
    productAnalysis.productInventory.forEach((productInfo, productKey) => {
        if (productInfo.groups.length > 1) {
            const chosenGroup = productPlacements.get(productKey);
            const originalGroups = productInfo.groups.filter(g => g !== chosenGroup);

            productMovements.push({
                productKey,
                productName: productInfo.productName,
                originalGroups,
                chosenGroup,
                groupSizes: productInfo.groupSizes
            });
        }
    });

    // Calculate seller diversity for deduplicated groups
    const deduplicatedSellerCounts = deduplicatedMatches.map(group => {
        const sellers = new Set();
        Object.keys(group).forEach(productKey => {
            const seller = productKey.split('_')[0];
            sellers.add(seller);
        });
        return sellers.size;
    });

    const deduplicatedSellerStats = calculateGroupStats(deduplicatedSellerCounts);

    // Create comprehensive report
    const report = {
        timestamp: new Date().toISOString(),
        deduplicationStrategy: {
            primaryCriteria: "seller_diversity",
            secondaryCriteria: "group_size",
            description: "Products retained in groups with highest seller diversity, with group size as tiebreaker"
        },
        summary: {
            originalGroups: originalMatches.length,
            deduplicatedGroups: deduplicatedMatches.length,
            groupsRemoved: originalMatches.length - deduplicatedMatches.length,
            totalUniqueProducts: stats.totalProducts,
            duplicatedProducts: stats.duplicatedProducts,
            duplicateInstancesRemoved: stats.totalDuplicationInstances,
            duplicationRate: ((stats.duplicatedProducts / stats.totalProducts) * 100).toFixed(2) + '%'
        },
        placementDecisions: placementStats,
        groupStatistics: {
            original: originalGroupStats,
            deduplicated: deduplicatedGroupStats
        },
        sellerDiversityStatistics: {
            original: stats.sellerDiversity,
            deduplicated: deduplicatedSellerStats
        },
        validation: validationResult,
        productMovements: {
            totalProductsMoved: productMovements.length,
            examples: productMovements.slice(0, 10) // First 10 examples
        },
        efficiency: {
            spaceSavings: {
                originalTotalEntries: originalMatches.reduce((sum, group) => sum + Object.keys(group).length, 0),
                deduplicatedTotalEntries: deduplicatedMatches.reduce((sum, group) => sum + Object.keys(group).length, 0)
            }
        }
    };

    // Calculate space savings
    report.efficiency.spaceSavings.entriesRemoved =
        report.efficiency.spaceSavings.originalTotalEntries - report.efficiency.spaceSavings.deduplicatedTotalEntries;
    report.efficiency.spaceSavings.reductionPercentage =
        ((report.efficiency.spaceSavings.entriesRemoved / report.efficiency.spaceSavings.originalTotalEntries) * 100).toFixed(2) + '%';

    // Save the report
    await fs.writeFile('deduplication-summary.json', JSON.stringify(report, null, 2));

    // Display summary to console
    console.log('   📋 DEDUPLICATION SUMMARY REPORT');
    console.log('   ================================');
    console.log(`   🎯 Strategy: Seller diversity first, group size as tiebreaker`);
    console.log(`   📊 Groups: ${report.summary.originalGroups.toLocaleString()} → ${report.summary.deduplicatedGroups.toLocaleString()} (${report.summary.groupsRemoved.toLocaleString()} removed)`);
    console.log(`   🔄 Products moved: ${report.productMovements.totalProductsMoved.toLocaleString()}`);
    console.log(`   📈 Placement decisions:`);
    console.log(`      • By seller diversity: ${placementStats.sellerDiversity.toLocaleString()}`);
    console.log(`      • By group size (tiebreaker): ${placementStats.groupSizeTiebreaker.toLocaleString()}`);
    console.log(`   🏪 Seller diversity improved:`);
    console.log(`      • Original avg: ${stats.sellerDiversity.avgSellers} sellers/group`);
    console.log(`      • Deduplicated avg: ${deduplicatedSellerStats.average} sellers/group`);
    console.log(`   📉 Duplicate entries removed: ${report.efficiency.spaceSavings.entriesRemoved.toLocaleString()} (${report.efficiency.spaceSavings.reductionPercentage})`);
    console.log(`   ✅ Data integrity: ${validationResult.isValid ? 'PRESERVED' : 'COMPROMISED'}`);
    console.log(`   💾 Report saved to: deduplication-summary.json\n`);
}

// Calculate statistics for group sizes
function calculateGroupStats(groupSizes) {
    if (groupSizes.length === 0) return null;

    const sorted = [...groupSizes].sort((a, b) => a - b);
    const sum = sorted.reduce((a, b) => a + b, 0);

    return {
        count: groupSizes.length,
        min: sorted[0],
        max: sorted[sorted.length - 1],
        average: (sum / groupSizes.length).toFixed(2),
        median: sorted.length % 2 === 0
            ? ((sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2).toFixed(2)
            : sorted[Math.floor(sorted.length / 2)],
        total: sum
    };
}

module.exports = { deduplicateMatches };

// Run the deduplication if this file is executed directly
if (require.main === module) {
    deduplicateMatches().catch(console.error);
}
