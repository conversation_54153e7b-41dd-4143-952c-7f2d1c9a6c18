const fs = require('fs');

const targetProducts = [
  'sham_4429071',
  'greco_503903', 
  'sysco_7122642',
  'sysco_8270631',
  'usfood_#2791630',
  'usfood_#2791663',
  'usfood_#7349632',
  'usfood_#1080758',
  'usfood_#6725496',
  'chef_5411046',
  'depot_2390230',
  'chef_2791630',
  'chef_2791663',
  'perf_544265',
  'chef_1375948',
  'perf_055575'
];

console.log('Target group has', targetProducts.length, 'products');

const sellers = new Set();
targetProducts.forEach(key => {
  sellers.add(key.split('_')[0]);
});
console.log('Target group has', sellers.size, 'sellers:', Array.from(sellers).join(', '));

// Now let's check what group 22839 actually contains
const matches = JSON.parse(fs.readFileSync('matches.json', 'utf8'));
const group22839 = matches[22839];

console.log('\nGroup 22839 contains:');
console.log('Products:', Object.keys(group22839).length);

const group22839Sellers = new Set();
Object.keys(group22839).forEach(key => {
  group22839Sellers.add(key.split('_')[0]);
});
console.log('Sellers:', group22839Sellers.size, ':', Array.from(group22839Sellers).join(', '));

console.log('\nGroup 22839 products:');
Object.entries(group22839).forEach(([key, name]) => {
  console.log(`  ${key}: ${name}`);
});
