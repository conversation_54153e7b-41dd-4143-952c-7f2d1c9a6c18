const fs = require('fs').promises;

// Validation script for deduplication algorithm
async function validateDeduplication() {
    console.log('🔍 COMPREHENSIVE DEDUPLICATION VALIDATION');
    console.log('==========================================\n');

    try {
        // Load data files
        console.log('📂 Loading data files...');
        const originalMatches = JSON.parse(await fs.readFile('matches.json', 'utf8'));
        const deduplicatedMatches = JSON.parse(await fs.readFile('matches-sh.json', 'utf8'));

        console.log(`✅ Original: ${originalMatches.length} groups`);
        console.log(`✅ Deduplicated: ${deduplicatedMatches.length} groups\n`);

        // Sample products for testing
        console.log('🎯 Selecting sample products for validation...');
        const sampleProducts = selectSampleProducts(originalMatches);
        console.log(`✅ Selected ${sampleProducts.length} products for testing\n`);

        // Build lookup maps
        console.log('🗺️ Building lookup maps...');
        const originalProductToGroups = buildOriginalProductMap(originalMatches);
        const deduplicatedProductToGroup = buildDeduplicatedProductMap(deduplicatedMatches);
        console.log('✅ Lookup maps created\n');

        // Perform validations
        const validationResults = {
            groupIntegrityViolations: [],
            sellerDiversityViolations: [],
            correctPlacements: 0,
            totalTested: sampleProducts.length,
            statistics: {
                originalAvgSellers: 0,
                deduplicatedAvgSellers: 0,
                sellerDiversityImproved: 0,
                sellerDiversityDecreased: 0
            }
        };

        console.log('🔍 VALIDATION 1: Group Integrity Check');
        console.log('=====================================');
        await validateGroupIntegrity(sampleProducts, originalMatches, deduplicatedMatches, deduplicatedProductToGroup, validationResults);

        console.log('\n🔍 VALIDATION 2: Seller Diversity Rule Check');
        console.log('============================================');
        await validateSellerDiversityRule(sampleProducts, originalProductToGroups, deduplicatedProductToGroup, deduplicatedMatches, validationResults);

        console.log('\n📊 FINAL VALIDATION REPORT');
        console.log('==========================');
        await generateValidationReport(validationResults);

    } catch (error) {
        console.error('❌ Validation failed:', error);
        throw error;
    }
}

// Select sample products from each seller
function selectSampleProducts(originalMatches) {
    const sellerProducts = {
        chef: new Set(),
        sham: new Set(),
        sysco: new Set(),
        usfood: new Set(),
        greco: new Set(),
        depot: new Set(),
        perf: new Set()
    };

    // Collect all products by seller
    originalMatches.forEach(group => {
        Object.keys(group).forEach(productKey => {
            const seller = productKey.split('_')[0];
            if (sellerProducts[seller]) {
                sellerProducts[seller].add(productKey);
            }
        });
    });

    // Sample 50 products from each seller (or all if less than 50)
    const sampleProducts = [];
    Object.entries(sellerProducts).forEach(([seller, products]) => {
        const productArray = Array.from(products);
        const sampleSize = Math.min(50, productArray.length);

        // Shuffle and take first sampleSize products
        for (let i = productArray.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [productArray[i], productArray[j]] = [productArray[j], productArray[i]];
        }

        const sampled = productArray.slice(0, sampleSize);
        sampleProducts.push(...sampled);

        console.log(`   ${seller}: ${sampled.length} products sampled`);
    });

    // Add the specific problematic product
    if (!sampleProducts.includes('chef_5411046')) {
        sampleProducts.push('chef_5411046');
        console.log('   Added chef_5411046 for specific testing');
    }

    return sampleProducts;
}

// Build map of product -> groups in original matches
function buildOriginalProductMap(originalMatches) {
    const productToGroups = new Map();

    originalMatches.forEach((group, groupIndex) => {
        Object.keys(group).forEach(productKey => {
            if (!productToGroups.has(productKey)) {
                productToGroups.set(productKey, []);
            }
            productToGroups.get(productKey).push({
                groupIndex,
                group: group,
                sellerCount: countSellers(group),
                productCount: Object.keys(group).length
            });
        });
    });

    return productToGroups;
}

// Build map of product -> group in deduplicated matches
function buildDeduplicatedProductMap(deduplicatedMatches) {
    const productToGroup = new Map();

    deduplicatedMatches.forEach((group, groupIndex) => {
        Object.keys(group).forEach(productKey => {
            productToGroup.set(productKey, {
                groupIndex,
                group: group,
                sellerCount: countSellers(group),
                productCount: Object.keys(group).length
            });
        });
    });

    return productToGroup;
}

// Count unique sellers in a group
function countSellers(group) {
    const sellers = new Set();
    Object.keys(group).forEach(productKey => {
        sellers.add(productKey.split('_')[0]);
    });
    return sellers.size;
}

// Validate group integrity
async function validateGroupIntegrity(sampleProducts, originalMatches, deduplicatedMatches, deduplicatedProductToGroup, validationResults) {
    let violations = 0;

    for (const productKey of sampleProducts) {
        const deduplicatedInfo = deduplicatedProductToGroup.get(productKey);

        if (!deduplicatedInfo) {
            validationResults.groupIntegrityViolations.push({
                productKey,
                issue: 'Product not found in deduplicated matches',
                severity: 'CRITICAL'
            });
            violations++;
            continue;
        }

        const deduplicatedGroup = deduplicatedInfo.group;

        // Find this exact group in original matches
        let foundInOriginal = false;
        for (let i = 0; i < originalMatches.length; i++) {
            const originalGroup = originalMatches[i];

            if (groupsAreIdentical(originalGroup, deduplicatedGroup)) {
                foundInOriginal = true;
                break;
            }
        }

        if (!foundInOriginal) {
            validationResults.groupIntegrityViolations.push({
                productKey,
                issue: 'Deduplicated group does not exist in original matches',
                deduplicatedGroup: Object.keys(deduplicatedGroup),
                deduplicatedGroupSize: Object.keys(deduplicatedGroup).length,
                deduplicatedSellerCount: deduplicatedInfo.sellerCount,
                severity: 'CRITICAL'
            });
            violations++;
        }
    }

    console.log(`   ✅ Tested: ${sampleProducts.length} products`);
    console.log(`   ${violations === 0 ? '✅' : '❌'} Violations: ${violations}`);

    if (violations > 0) {
        console.log(`   ⚠️  ${violations} products have group integrity violations!`);
    }
}

// Check if two groups are identical
function groupsAreIdentical(group1, group2) {
    const keys1 = Object.keys(group1).sort();
    const keys2 = Object.keys(group2).sort();

    if (keys1.length !== keys2.length) return false;

    for (let i = 0; i < keys1.length; i++) {
        if (keys1[i] !== keys2[i]) return false;
    }

    return true;
}

// Validate seller diversity rule
async function validateSellerDiversityRule(sampleProducts, originalProductToGroups, deduplicatedProductToGroup, deduplicatedMatches, validationResults) {
    let violations = 0;
    let totalOriginalSellers = 0;
    let totalDeduplicatedSellers = 0;

    for (const productKey of sampleProducts) {
        const originalGroups = originalProductToGroups.get(productKey);
        const deduplicatedInfo = deduplicatedProductToGroup.get(productKey);

        if (!originalGroups || !deduplicatedInfo) continue;

        // Find the group with maximum sellers in original
        let maxSellers = 0;
        let maxSellerGroups = [];

        originalGroups.forEach(groupInfo => {
            if (groupInfo.sellerCount > maxSellers) {
                maxSellers = groupInfo.sellerCount;
                maxSellerGroups = [groupInfo];
            } else if (groupInfo.sellerCount === maxSellers) {
                maxSellerGroups.push(groupInfo);
            }
        });

        // Among groups with max sellers, find the one with most products
        let bestGroup = maxSellerGroups[0];
        maxSellerGroups.forEach(groupInfo => {
            if (groupInfo.productCount > bestGroup.productCount) {
                bestGroup = groupInfo;
            }
        });

        totalOriginalSellers += bestGroup.sellerCount;
        totalDeduplicatedSellers += deduplicatedInfo.sellerCount;

        // Check if the chosen group follows the rule
        if (deduplicatedInfo.sellerCount < maxSellers) {
            validationResults.sellerDiversityViolations.push({
                productKey,
                issue: 'Chosen group does not have maximum seller diversity',
                chosenSellers: deduplicatedInfo.sellerCount,
                chosenProducts: deduplicatedInfo.productCount,
                maxAvailableSellers: maxSellers,
                bestGroupProducts: bestGroup.productCount,
                allOriginalGroups: originalGroups.map(g => ({
                    sellers: g.sellerCount,
                    products: g.productCount
                })),
                severity: 'HIGH'
            });
            violations++;
        } else if (deduplicatedInfo.sellerCount === maxSellers) {
            // Check if it has the most products among groups with max sellers
            if (deduplicatedInfo.productCount < bestGroup.productCount) {
                validationResults.sellerDiversityViolations.push({
                    productKey,
                    issue: 'Chosen group does not have maximum products among groups with same seller count',
                    chosenSellers: deduplicatedInfo.sellerCount,
                    chosenProducts: deduplicatedInfo.productCount,
                    bestGroupSellers: bestGroup.sellerCount,
                    bestGroupProducts: bestGroup.productCount,
                    severity: 'MEDIUM'
                });
                violations++;
            } else {
                validationResults.correctPlacements++;
            }
        }
    }

    validationResults.statistics.originalAvgSellers = (totalOriginalSellers / sampleProducts.length).toFixed(2);
    validationResults.statistics.deduplicatedAvgSellers = (totalDeduplicatedSellers / sampleProducts.length).toFixed(2);

    console.log(`   ✅ Tested: ${sampleProducts.length} products`);
    console.log(`   ${violations === 0 ? '✅' : '❌'} Violations: ${violations}`);
    console.log(`   📊 Avg sellers - Original: ${validationResults.statistics.originalAvgSellers}, Deduplicated: ${validationResults.statistics.deduplicatedAvgSellers}`);

    if (violations > 0) {
        console.log(`   ⚠️  ${violations} products violate seller diversity rules!`);
    }
}

// Generate comprehensive validation report
async function generateValidationReport(validationResults) {
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            totalTested: validationResults.totalTested,
            correctPlacements: validationResults.correctPlacements,
            groupIntegrityViolations: validationResults.groupIntegrityViolations.length,
            sellerDiversityViolations: validationResults.sellerDiversityViolations.length,
            overallSuccess: validationResults.groupIntegrityViolations.length === 0 && validationResults.sellerDiversityViolations.length === 0
        },
        statistics: validationResults.statistics,
        violations: {
            groupIntegrity: validationResults.groupIntegrityViolations,
            sellerDiversity: validationResults.sellerDiversityViolations
        }
    };

    // Save detailed report
    await fs.writeFile('validation-report.json', JSON.stringify(report, null, 2));

    // Console summary
    console.log(`📊 VALIDATION SUMMARY:`);
    console.log(`   Total products tested: ${report.summary.totalTested}`);
    console.log(`   Correct placements: ${report.summary.correctPlacements}`);
    console.log(`   Group integrity violations: ${report.summary.groupIntegrityViolations}`);
    console.log(`   Seller diversity violations: ${report.summary.sellerDiversityViolations}`);
    console.log(`   Overall validation: ${report.summary.overallSuccess ? '✅ PASSED' : '❌ FAILED'}`);

    if (report.summary.groupIntegrityViolations > 0) {
        console.log(`\n❌ GROUP INTEGRITY VIOLATIONS (${report.summary.groupIntegrityViolations}):`);
        validationResults.groupIntegrityViolations.slice(0, 5).forEach((violation, index) => {
            console.log(`   ${index + 1}. ${violation.productKey}: ${violation.issue}`);
            if (violation.deduplicatedGroupSize) {
                console.log(`      Deduplicated group: ${violation.deduplicatedGroupSize} products, ${violation.deduplicatedSellerCount} sellers`);
            }
        });
        if (report.summary.groupIntegrityViolations > 5) {
            console.log(`      ... and ${report.summary.groupIntegrityViolations - 5} more violations`);
        }
    }

    if (report.summary.sellerDiversityViolations > 0) {
        console.log(`\n❌ SELLER DIVERSITY VIOLATIONS (${report.summary.sellerDiversityViolations}):`);
        validationResults.sellerDiversityViolations.slice(0, 5).forEach((violation, index) => {
            console.log(`   ${index + 1}. ${violation.productKey}: ${violation.issue}`);
            console.log(`      Chosen: ${violation.chosenSellers} sellers, ${violation.chosenProducts} products`);
            if (violation.maxAvailableSellers) {
                console.log(`      Available: ${violation.maxAvailableSellers} sellers, ${violation.bestGroupProducts} products`);
            }
        });
        if (report.summary.sellerDiversityViolations > 5) {
            console.log(`      ... and ${report.summary.sellerDiversityViolations - 5} more violations`);
        }
    }

    // Special check for chef_5411046
    const chef5411046Violation = validationResults.groupIntegrityViolations.find(v => v.productKey === 'chef_5411046') ||
                                 validationResults.sellerDiversityViolations.find(v => v.productKey === 'chef_5411046');

    if (chef5411046Violation) {
        console.log(`\n🔍 SPECIFIC ISSUE WITH chef_5411046:`);
        console.log(`   Issue: ${chef5411046Violation.issue}`);
        if (chef5411046Violation.chosenSellers) {
            console.log(`   Chosen group: ${chef5411046Violation.chosenSellers} sellers, ${chef5411046Violation.chosenProducts} products`);
        }
        if (chef5411046Violation.maxAvailableSellers) {
            console.log(`   Best available: ${chef5411046Violation.maxAvailableSellers} sellers, ${chef5411046Violation.bestGroupProducts} products`);
        }
    }

    console.log(`\n💾 Detailed report saved to: validation-report.json`);

    return report;
}

module.exports = { validateDeduplication };

// Run validation if this file is executed directly
if (require.main === module) {
    validateDeduplication().catch(console.error);
}
